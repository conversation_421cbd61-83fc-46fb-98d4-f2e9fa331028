{"$schema": "https://json.schemastore.org/package.json", "private": true, "type": "module", "scripts": {"build": "vite build", "dev": "vite", "format": "prettier --write resources/", "format:check": "prettier --check resources/", "lint": "eslint . --fix"}, "devDependencies": {"@eslint/js": "^9.19.0", "@iconify/vue": "^4.3.0", "@tailwindcss/vite": "^4.0.0", "@vitejs/plugin-vue": "^6.0.0", "@vue/eslint-config-typescript": "^14.3.0", "axios": "^1.8.2", "concurrently": "^9.0.1", "eslint-config-prettier": "^10.0.1", "eslint-plugin-vue": "^9.32.0", "laravel-vite-plugin": "^1.3.0", "prettier": "^3.4.2", "prettier-plugin-organize-imports": "^4.1.0", "prettier-plugin-tailwindcss": "^0.6.11", "sass": "1.77.6", "tailwindcss": "^4.0.0", "typescript-eslint": "^8.23.0", "unplugin-vue-components": "^28.5.0", "vite": "^6.2.4", "vite-plugin-vue-devtools": "^8.0.0", "vue": "^3.5.17", "vue-tsc": "^2.2.4"}, "dependencies": {"@inertiajs/vue3": "^2.0.15", "@tiptap/extension-blockquote": "^3.0.7", "@tiptap/extension-bold": "^3.0.7", "@tiptap/extension-bullet-list": "^3.0.7", "@tiptap/extension-code": "^3.0.7", "@tiptap/extension-code-block": "^3.0.7", "@tiptap/extension-color": "^3.0.7", "@tiptap/extension-heading": "^3.0.7", "@tiptap/extension-horizontal-rule": "^3.0.7", "@tiptap/extension-image": "^3.0.7", "@tiptap/extension-italic": "^3.0.7", "@tiptap/extension-link": "^3.0.7", "@tiptap/extension-ordered-list": "^3.0.7", "@tiptap/extension-strike": "^3.0.7", "@tiptap/extension-table": "^3.0.7", "@tiptap/extension-table-cell": "^3.0.7", "@tiptap/extension-table-header": "^3.0.7", "@tiptap/extension-table-row": "^3.0.7", "@tiptap/extension-text-align": "^3.0.7", "@tiptap/extension-text-style": "^3.0.7", "@tiptap/extension-underline": "^3.0.7", "@tiptap/pm": "^3.0.7", "@tiptap/starter-kit": "^3.0.7", "@tiptap/vue-3": "^3.0.7", "@vueuse/core": "^13.1.0", "bootstrap": "^5.3.5", "bootstrap-vue-next": "^0.28.6", "choices.js": "^11.1.0", "flatpickr": "^4.6.13", "pinia": "^3.0.2", "simplebar": "^6.3.0", "simplebar-vue": "^2.4.0", "sweetalert2": "^11.19.1", "typescript": "^5.2.2", "vue-i18n": "^11.1.11", "ziggy-js": "^2.4.2"}}