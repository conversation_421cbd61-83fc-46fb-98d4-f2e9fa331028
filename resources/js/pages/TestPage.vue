<template>
    <VerticalLayout>
        <PageTitle title="Users" subtitle="Users CRUD" />

        <b-row>
            <b-col cols="12">
                <b-card no-body>
                    <b-card-header class="border-bottom border-light">
                        <div class="d-flex justify-content-between flex-wrap gap-2">
                            <div class="position-relative">
                                <input type="text" class="form-control ps-4" placeholder="Search Company" />
                                <i class="ti ti-search position-absolute translate-middle-y top-50 ms-2"></i>
                            </div>

                            <div>
                                <Link :href="route('testpage2', { language: 'el' })" class="btn btn-primary"
                                    ><i class="ti ti-plus me-1"></i>Add User
                                </Link>
                            </div>
                        </div>
                    </b-card-header>

                    <b-table-simple responsive hover class="mb-0 text-nowrap">
                        <b-thead class="bg-light-subtle">
                            <b-tr>
                                <b-th class="ps-3" style="width: 50px">
                                    <b-form-checkbox />
                                </b-th>
                                <b-th>ID</b-th>
                                <b-th>Name</b-th>
                                <b-th>Email</b-th>
                                <b-th>Registered At</b-th>
                                <b-th class="text-center" style="width: 120px">Actions</b-th>
                            </b-tr>
                        </b-thead>
                        <b-tbody>
                            <b-tr v-for="(user, index) in users.data" :key="`user_${user.id}`">
                                <b-td class="ps-3">
                                    <b-form-checkbox />
                                </b-td>
                                <b-td>{{ user.id }}</b-td>
                                <b-td>{{ user.name }}</b-td>
                                <b-td>{{ user.email }}</b-td>
                                <b-td>{{ user.registered_at }}</b-td>

                                <b-td class="pe-3">
                                    <div class="hstack justify-content-end gap-1">
                                        <a href="javascript:void(0);" class="btn btn-soft-primary btn-icon btn-sm rounded-circle">
                                            <i class="ti ti-eye"></i
                                        ></a>
                                        <a href="javascript:void(0);" class="btn btn-soft-success btn-icon btn-sm rounded-circle">
                                            <i class="ti ti-edit fs-16"></i
                                        ></a>
                                        <a href="javascript:void(0);" class="btn btn-soft-danger btn-icon btn-sm rounded-circle">
                                            <i class="ti ti-trash"></i
                                        ></a>
                                    </div>
                                </b-td>
                            </b-tr>
                        </b-tbody>
                    </b-table-simple>

                    <b-card-footer>
                        <b-pagination
                            v-model="currentPage"
                            :total-rows="users.meta.total"
                            :per-page="users.meta.per_page"
                            first-text="First"
                            prev-text="Prev"
                            next-text="Next"
                            last-text="Last"
                            @update:model-value="goToPage"
                            class="justify-content-end mb-0"
                        ></b-pagination>
                    </b-card-footer>
                </b-card>
            </b-col>
        </b-row>
    </VerticalLayout>
</template>

<script setup lang="ts">
import PageTitle from '@/components/PageTitle.vue';
import VerticalLayout from '@/layouts/VerticalLayout.vue';
import type { UsersPageProps } from '@/types/user';
import { Link, router } from '@inertiajs/vue3';
import { ref, watch } from 'vue';

const props = defineProps<UsersPageProps>();
const users = props.users;

console.log('AAAAAAAAAAAAAAAAAAAAAAAAAA', users);

const currentPage = ref(users.meta.current_page);

const goToPage = (page: any) => {
    console.log('page clicked', page);
    if (page !== users.meta.current_page) {
        const targetLink = users.links.find((link) => link.label === page.toString() && link.url !== null);

        if (targetLink && targetLink.url) {
            router.visit(targetLink.url);
        }
    }
};

// Keep currentPage in sync if props change
watch(
    () => users.meta.current_page,
    (val) => {
        currentPage.value = val;
    },
);
</script>
