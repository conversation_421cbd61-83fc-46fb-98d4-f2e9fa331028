<template>
    <VerticalLayout>
        <!--        todo change subtitleRoute-->
        <PageTitle icon="ti ti-door" :title="editMode ? t('rooms.edit') : t('rooms.add')" :subtitle="t('rooms.list')" subtitleRoute="roomAdd" />

        <b-row>
            <b-col xl="6" lg="12">
                <b-card no-body>
                    <b-card-body>
                        <b-form-group :label="t('common.title')" label-for="title" class="mb-3">
                            <b-form-input type="text" name="title" :placeholder="t('rooms.editPlaceholder')" />
                        </b-form-group>

                        <b-form-group :label="t('common.description')" label-for="description" class="mb-3">
                            <text-editor v-model="room.description" />
                        </b-form-group>
                    </b-card-body>
                </b-card>
            </b-col>
            <b-col xl="6" lg="12">
                <b-card no-body>
                    <b-card-body> </b-card-body>
                </b-card>
            </b-col>
        </b-row>

        <b-row>
            <b-col lg="7">
                <b-card no-body>
                    <b-card-header class="border-bottom border-dashed">
                        <b-card-title tag="h4" class="mb-0">Basic information</b-card-title>
                    </b-card-header>
                    <b-card-body>
                        <b-row>
                            <!-- TODO: If edit, we should use PUT and restrict routes as well -->
                            <form method="post" action="">
                                <input type="hidden" name="_token" :value="csrf" />
                                <b-col lg="12">
                                    <b-form-group label="Room title" label-for="title" class="mb-3">
                                        <b-form-input v-if="room" type="text" v-model="room.title" name="title" placeholder="title" />
                                        <b-form-input v-if="room" type="hidden" v-model="room.id" name="id" placeholder="title" />
                                        <div v-if="!room">
                                            <b-form-input type="text" name="title" placeholder="title" />
                                        </div>
                                    </b-form-group>
                                </b-col>
                                <b-col lg="12">
                                    <label>Description (text)</label>
                                    <br />
                                    <textarea v-model="room.description" name="description"></textarea>
                                </b-col>
                                <b-col lg="12">
                                    <label>Admin of the room</label>
                                    <br />
                                    <select v-model="room.adminId" name="adminId">
                                        <option :value="user.id" v-for="(user, index) in users">{{ user.email }}</option>
                                    </select>
                                </b-col>
                                <b-col lg="12">
                                    <label>Labels</label>
                                    <br />
                                    <select v-model="room.tagIds" multiple name="tagIds[]">
                                        <option :value="tag.id" v-for="(tag, index) in tags">{{ tag.title }}</option>
                                    </select>
                                </b-col>
                                <b-col lg="12">
                                    <label>Individual Users</label>
                                    <br />
                                    <select v-model="room.userIds" multiple name="userIds[]">
                                        <option :value="user.id" v-for="(user, index) in users">{{ user.email }}</option>
                                    </select>
                                </b-col>
                                <b-col lg="12">
                                    <button type="submit" class="btn btn-primary">save</button>
                                </b-col>
                            </form>
                        </b-row>
                    </b-card-body>
                </b-card>
            </b-col>
        </b-row>
    </VerticalLayout>
</template>

<script setup lang="ts">
import PageTitle from '@/components/PageTitle.vue';
import TextEditor from '@/components/TextEditor.vue';
import VerticalLayout from '@/layouts/VerticalLayout.vue';
import { usePage } from '@inertiajs/vue3';
import { BCard, BCol, BRow } from 'bootstrap-vue-next';
import { ref } from 'vue';
import { useI18n } from 'vue-i18n';
const { t } = useI18n();

const csrf = usePage().props.csrf_token;
const room = usePage().props.room ?? {
    id: null,
    title: '',
    description: '',
    adminId: null,
    tagIds: [],
    userIds: [],
};
const tags = usePage().props.tags ?? null;
const users = usePage().props.users ?? null;

const editMode = ref(!!room);
</script>
