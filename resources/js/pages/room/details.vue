<template>
    <VerticalLayout>
        <!--        todo change subtitleRoute-->
        <PageTitle icon="ti ti-door" :title="editMode ? t('rooms.edit') : t('rooms.add')" :subtitle="t('rooms.list')" subtitleRoute="roomAdd" />

        <b-row>
            <b-col xl="6" lg="12">
                <b-card no-body>
                    <b-card-body>
                        <b-form-group :label="t('common.title')" label-for="title" class="mb-3">
                            <b-form-input v-model="form.title" type="text" name="title" :placeholder="t('rooms.editPlaceholder')" />
                        </b-form-group>

                        <b-form-group :label="t('common.description')" label-for="description" class="mb-3">
                            <textarea v-model="form.description" />
                        </b-form-group>
                    </b-card-body>
                </b-card>
            </b-col>
            <b-col xl="6" lg="12">
                <b-card no-body>
                    <b-card-body>
                        <b-form-group :label="t('tags.title')" label-for="tags" class="mb-3">
                            <ChoicesInput
                                id="choices-text-unique-values"
                                label="Unique values only, no pasting"
                                v-model="selectedTagNames"
                                name="tags"
                                :choice-options="{ removeItemButton: true, duplicateItemsAllowed: false, paste: false }"
                            />
                        </b-form-group>
                    </b-card-body>
                </b-card>
            </b-col>
        </b-row>

        <b-row>
            <b-col lg="7">
                <b-card no-body>
                    <b-card-body>
                        <b-row>
                            <b-col lg="12"> </b-col>

                            <b-col lg="12">
                                <label>Admin of the room</label>
                                <br />
                            </b-col>
                            <b-col lg="12">
                                <label>Labels</label>
                                <br />
                            </b-col>
                            <b-col lg="12">
                                <label>Individual Users</label>
                                <br />
                            </b-col>
                            <b-col lg="12">
                                <button type="submit" class="btn btn-primary">save</button>
                            </b-col>
                        </b-row>
                    </b-card-body>
                </b-card>
            </b-col>
        </b-row>
    </VerticalLayout>
</template>

<script setup lang="ts">
import ChoicesInput from '@/components/ChoicesInput.vue';
import PageTitle from '@/components/PageTitle.vue';
import VerticalLayout from '@/layouts/VerticalLayout.vue';
import { Room, RoomFormData } from '@/types/room';
import { Tag } from '@/types/tag';
import { User } from '@/types/user';
import { BCard, BCol, BRow } from 'bootstrap-vue-next';
import { computed, PropType, reactive, ref } from 'vue';
import { useI18n } from 'vue-i18n';
const { t } = useI18n();

// Props
const props = defineProps({
    room: {
        type: Object as PropType<Room>,
        required: false,
    },
    users: {
        type: Array as PropType<User[]>,
        required: true,
    },
    tags: {
        type: Array as PropType<Tag[]>,
        required: true,
    },
});

// Initialize form data
const form = reactive<RoomFormData>({
    title: props.room?.title ?? '',
    description: props.room?.description ?? '',
    adminId: props.room?.adminId ?? null,
    tags: props.room?.tags ?? [],
    users: props.room?.users ?? [],
});

const editMode = ref(!!props.room);

const selectedTagNames = computed<string[]>({
    get() {
        // Map selected tag IDs to names
        return form.tags
            .map((id) => props.tags.find((t) => t.id === id))
            .filter(Boolean)
            .map((t) => t!.title);
    },
    set(newNames: string[]) {
        // Map tag names to IDs for `form.tags`
        form.tags = newNames
            .map((title) => props.tags.find((t) => t.title === title))
            .filter(Boolean)
            .map((t) => t!.id);
    },
});
</script>
