<template>
    <VerticalLayout>
        <PageTitle icon="ti ti-door" :title="editMode ? t('rooms.edit') : t('rooms.add')" :subtitle="t('rooms.list')" subtitleRoute="roomIndex" />

        <b-card no-body>
            <b-card-body>
                <b-row>
                    <b-col xl="6" lg="12">
                        <b-form-group :label="t('common.title')" label-for="title" class="mb-3">
                            <b-form-input v-model="form.title" type="text" name="title" :placeholder="t('rooms.editPlaceholder')" />
                        </b-form-group>
                        <b-form-group :label="t('common.admin')" label-for="adminId" class="mb-3">
                            <ChoicesSelect id="adminId" v-model="form.adminId" :options="usersLookup" class="form-control" />
                        </b-form-group>
                    </b-col>
                    <b-col xl="6" lg="12">
                        <b-form-group :label="t('tags.title')" label-for="tagsLookup" class="mb-3">
                            <ChoicesSelect
                                id="tagsLookup"
                                v-model="selectedTagNames"
                                :options="tagsLookup"
                                :choice-options="{ addItems: false, removeItemButton: true, duplicateItemsAllowed: false, paste: false }"
                                :multiple="true"
                            />
                        </b-form-group>

                        <b-form-group :label="t('users.title')" label-for="usersLookup" class="mb-3">
                            <ChoicesSelect
                                id="usersLookup"
                                v-model="selectedUsersNames"
                                :options="usersLookup"
                                :choice-options="{ addItems: false, removeItemButton: true, duplicateItemsAllowed: false, paste: false }"
                                :multiple="true"
                            />
                        </b-form-group>
                    </b-col>
                </b-row>
            </b-card-body>
        </b-card>

        <b-card no-body>
            <div class="d-flex flex-wrap gap-2">
                <button v-if="editMode" type="submit" class="btn btn-primary">{{ t('common.update') }}</button>
                <button v-else @click="addRoom" type="submit" class="btn btn-primary">{{ t('common.save') }}</button>
                <Link :href="route('roomIndex')" class="btn btn-secondary"> <i class="ti ti-arrow-left me-1"></i>{{ t('common.backToList') }}</Link>
            </div>
        </b-card>
        <b-card no-body>
            <b-card-body>
                <b-form-group :label="t('common.description')" label-for="description" class="mb-3">
                    <textarea v-model="form.description" />
                </b-form-group>
            </b-card-body>
        </b-card>
    </VerticalLayout>
</template>

<script setup lang="ts">
import ChoicesSelect from '@/components/ChoicesSelect.vue';
import PageTitle from '@/components/PageTitle.vue';
import VerticalLayout from '@/layouts/VerticalLayout.vue';
import { Room, RoomFormData } from '@/types/room';
import { Tag } from '@/types/tag';
import { User } from '@/types/user';
import { Link } from '@inertiajs/vue3';
import { BCard, BCol, BRow } from 'bootstrap-vue-next';
import { computed, PropType, reactive, ref } from 'vue';
import { useI18n } from 'vue-i18n';
const { t } = useI18n();

// Props
const props = defineProps({
    room: {
        type: Object as PropType<Room>,
        required: false,
    },
    users: {
        type: Array as PropType<User[]>,
        required: true,
    },
    tags: {
        type: Array as PropType<Tag[]>,
        required: true,
    },
});

const tagsLookup = computed(() =>
    props.tags.map((tag) => ({
        value: tag.id,
        text: tag.title,
    })),
);

const usersLookup = computed(() =>
    props.users.map((user) => ({
        value: user.id,
        text: user.name,
    })),
);

// Initialize form data
const form = reactive<RoomFormData>({
    title: props.room?.title ?? '',
    description: props.room?.description ?? '',
    adminId: props.room?.adminId ?? null,
    tags: props.room?.tags ?? [],
    users: props.room?.users ?? [],
});

const editMode = ref(!!props.room);

const selectedTagNames = computed<string[]>({
    get() {
        return form.tags
            .map((id) => props.tags.find((t) => t.id === id))
            .filter(Boolean)
            .map((t) => t!.title);
    },
    set(newNames: string | string[]) {
        const namesArray = Array.isArray(newNames) ? newNames : [newNames];
        form.tags = namesArray
            .map((title) => props.tags.find((t) => t.title === title))
            .filter(Boolean)
            .map((t) => t!.id);
    },
});

const selectedUsersNames = computed<string[]>({
    get() {
        return form.users
            .map((id) => props.users.find((t) => t.id === id))
            .filter(Boolean)
            .map((t) => t!.name);
    },
    set(newNames: string | string[]) {
        const namesArray = Array.isArray(newNames) ? newNames : [newNames];
        form.users = namesArray
            .map((name) => props.users.find((t) => t.name === name))
            .filter(Boolean)
            .map((t) => t!.id);
    },
});
</script>
