<template>
    <VerticalLayout>
        <PageTitle title="Tags" subtitle="Tags CRUD" />

        <b-row>
            <b-col cols="12">
                <b-card no-body>
                    <b-card-header class="border-bottom border-light">
                        <div class="d-flex justify-content-between flex-wrap gap-2">
                            <div class="position-relative">
                                <input type="text" class="form-control ps-4 pe-4" v-model="searchInput" placeholder="Search Tags" />
                                <i class="ti ti-search position-absolute translate-middle-y top-50 ms-2"></i>
                                <i
                                    v-if="searchInput.length"
                                    @click="clearSearch"
                                    class="pq-cursor-pointer ti ti-x position-absolute translate-middle-y end-0 top-50 p-2"
                                ></i>
                            </div>
                            <div>
                                <Link :href="route('tagAdd')" class="btn btn-primary"> <i class="ti ti-plus me-1"></i>Add Tag </Link>
                            </div>
                        </div>
                    </b-card-header>

                    <b-table-simple responsive hover class="mb-0 text-nowrap">
                        <b-thead class="bg-light-subtle">
                            <b-tr>
                                <b-th class="ps-3" style="width: 50px">
                                    <b-form-checkbox />
                                </b-th>
                                <b-th>ID</b-th>
                                <b-th>Title</b-th>
                                <b-th>System</b-th>
                                <b-th class="text-center" style="width: 120px">Actions</b-th>
                            </b-tr>
                        </b-thead>
                        <b-tbody>
                            <b-tr v-for="tag in tags.data" :key="`tag_${tag.id}`">
                                <b-td class="ps-3">
                                    <b-form-checkbox />
                                </b-td>
                                <b-td>{{ tag.id }}</b-td>
                                <b-td>{{ tag.title }}</b-td>
                                <b-td>{{ tag.system ? 'Yes' : 'No' }}</b-td>

                                <b-td class="pe-3">
                                    <div class="hstack justify-content-end gap-1">
                                        <template v-if="!tag.system">
                                            <Link
                                                :href="route('updateTag', { id: tag.id })"
                                                class="btn btn-soft-success btn-icon btn-sm rounded-circle"
                                                ><i class="ti ti-edit fs-16"></i
                                            ></Link>

                                            <button @click="deleteTagWithConfirm(tag.id)" class="btn btn-soft-danger btn-icon btn-sm rounded-circle">
                                                <i class="ti ti-trash"></i>
                                            </button>
                                        </template>
                                        <span v-else class="badge bg-danger rounded-pill me-1">System tag</span>
                                    </div>
                                </b-td>
                            </b-tr>
                        </b-tbody>
                    </b-table-simple>

                    <b-card-footer>
                        <b-pagination
                            v-model="currentPage"
                            :total-rows="tags.total"
                            :per-page="tags.per_page"
                            first-text="First"
                            prev-text="Prev"
                            next-text="Next"
                            last-text="Last"
                            @update:model-value="goToPage"
                            class="justify-content-end mb-0"
                        ></b-pagination>
                    </b-card-footer>
                </b-card>
            </b-col>
        </b-row>
    </VerticalLayout>
</template>

<script setup lang="ts">
import PageTitle from '@/components/PageTitle.vue';
import VerticalLayout from '@/layouts/VerticalLayout.vue';
import { Link, router, usePage } from '@inertiajs/vue3';
import { BRow } from 'bootstrap-vue-next';
import Swal from 'sweetalert2';
import { ref, watch } from 'vue';
import { useI18n } from 'vue-i18n';
const { t } = useI18n();

// Define props passed from Laravel
const props = defineProps<{
    searchKey?: string;
    tags: {
        data: Array<{
            id: number;
            title: string;
            system: boolean;
        }>;
        current_page: number;
        per_page: number;
        total: number;
        links: Array<{
            url: string | null;
            label: string;
            active: boolean;
        }>;
    };
}>();

// SEARCH FUNCTIONALITY
const initialSearchKey = usePage().props.searchKey;
const searchInput = ref(typeof initialSearchKey === 'string' ? initialSearchKey : '');

// Debounced search
let searchTimeout: number;
watch(searchInput, (newValue) => {
    clearTimeout(searchTimeout);
    searchTimeout = setTimeout(() => {
        router.post(route('tagIndexPost'), {
            searchKey: newValue,
        });
    }, 500); // 500ms debounce
});
const clearSearch = () => {
    searchInput.value = '';
};

// PAGINATION
// Local state for current page
const currentPage = ref(props.tags.current_page);
// Go to page handler
const goToPage = (page: number) => {
    if (page !== props.tags.current_page) {
        const targetLink = props.tags.links.find((link) => link.label === page.toString() && link.url !== null);

        if (targetLink?.url) {
            router.visit(targetLink.url);
        }
    }
};
// Sync local currentPage with props
watch(
    () => props.tags.current_page,
    (val) => {
        currentPage.value = val;
    },
);
// END PAGINATION

// ACTIONS
const deleteTagWithConfirm = (id: number) => {
    Swal.fire({
        title: t('alerts.common.delete.title'),
        text: t('alerts.common.delete.text'),
        icon: 'warning',
        showCancelButton: true,
        confirmButtonColor: '#d33',
        cancelButtonColor: '#3085d6',
        confirmButtonText: t('alerts.common.delete.confirm'),
        cancelButtonText: t('alerts.common.delete.cancel'),
    }).then((result) => {
        if (result.isConfirmed) {
            // Call the delete route via Inertia router
            router.delete(route('deleteTag', id), {
                onSuccess: () => {
                    Swal.fire(t('alerts.common.delete.successTitle'), t('alerts.tags.delete.success'), 'success');
                },
                onError: (errors) => {
                    Swal.fire(t('alerts.common.delete.errorTitle'), errors.message || t('alerts.tags.delete.error'), 'error');
                },
            });
        }
    });
};
// END ACTIONS
</script>
