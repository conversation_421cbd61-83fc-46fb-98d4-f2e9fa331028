<template>
    <VerticalLayout>
        <PageTitle icon="ti ti-tag" :title="editMode ? t('tags.edit') : t('tags.add')" :subtitle="t('tags.list')" subtitleRoute="tagIndex" />

        <b-row>
            <b-col lg="7">
                <b-card no-body>
                    <b-card-body>
                        <b-row>
                            <b-col lg="12">
                                <b-form-group :label="t('common.title')" label-for="title" class="mb-3">
                                    <b-form-input type="text" v-model="tagName" name="title" :placeholder="t('tags.editPlaceholder')" />
                                </b-form-group>
                            </b-col>
                            <div class="d-flex flex-wrap gap-2">
                                <button v-if="editMode" @click="updateTag" type="submit" class="btn btn-primary">{{ t('common.update') }}</button>
                                <button v-else @click="addTag" type="submit" class="btn btn-primary">{{ t('common.save') }}</button>
                                <Link :href="route('tagIndex')" class="btn btn-secondary">
                                    <i class="ti ti-arrow-left me-1"></i>{{ t('common.backToList') }}</Link
                                >
                            </div>
                        </b-row>
                    </b-card-body>
                </b-card>
            </b-col>
        </b-row>
    </VerticalLayout>
</template>

<script setup lang="ts">
import PageTitle from '@/components/PageTitle.vue';
import VerticalLayout from '@/layouts/VerticalLayout.vue';
import type { Tag } from '@/types/tag';
import { Link, router, usePage } from '@inertiajs/vue3';
import { BRow } from 'bootstrap-vue-next';
import Swal from 'sweetalert2';
import { ref } from 'vue';
import { useI18n } from 'vue-i18n';
const { t } = useI18n();

const props = usePage<{ tag?: Tag }>().props;
const tag: Tag | null = props.tag ?? null;

const tagName = ref(tag ? tag.title : '');
const editMode = ref(!!tag);

const addTag = () => {
    router.post(
        route('addTagPost'),
        { title: tagName.value },
        {
            onSuccess: () => {
                Swal.fire(t('alerts.common.add.successTitle'), t('alerts.tags.add.success'), 'success');
                tagName.value = '';
            },
            onError: (err) => {
                console.error('Error adding tag:', err);
            },
        },
    );
};

const updateTag = () => {
    router.post(
        route('updateTagPost', { id: tag?.id }),
        { id: tag?.id, title: tagName.value },
        {
            onSuccess: () => {
                Swal.fire(t('alerts.common.update.successTitle'), t('alerts.tags.update.success'), 'success');
            },
            onError: (err) => {
                console.error('Error updating tag:', err);
            },
        },
    );
};
</script>
