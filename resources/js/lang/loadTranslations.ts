export type LocaleMessages = Record<string, any>;
export type Messages = Record<string, LocaleMessages>;

export function loadLocaleMessages(): Messages {
    // import all json files matching ./[lang]/*.json
    // The value returned by import.meta.glob is a Record<string, () => Promise<{ default: object }>>
    const modules = import.meta.glob<{ default: LocaleMessages }>('./*/**.json', { eager: true });
    const messages: Messages = {};

    // Loop through all found modules
    Object.entries(modules).forEach(([path, mod]) => {
        // path example: './en/common.json'
        const match = path.match(/\.\/(\w+)\/.+\.json$/);
        if (!match) return;
        const lang = match[1];
        messages[lang] = messages[lang] || {};
        Object.assign(messages[lang], mod.default);
    });

    return messages;
}
