export function loadLocaleMessages() {
    const locales = import.meta.glob('./*/**.json', { eager: true });
    const messages = {};

    Object.keys(locales).forEach((key) => {
        const [, lang, file] = key.match(/\.\/(\w+)\/(.*)\.json$/) || [];
        if (!lang) return;
        messages[lang] = messages[lang] || {};
        Object.assign(messages[lang], locales[key].default);
    });

    return messages;
}
