import '@/scss/app.scss';
import '@/scss/icons.scss';
import 'bootstrap-vue-next/dist/bootstrap-vue-next.css';
import 'choices.js/src/styles/choices.scss';
import 'flatpickr/dist/flatpickr.css';
import 'simplebar';

import { createInertiaApp } from '@inertiajs/vue3';
import { createBootstrap } from 'bootstrap-vue-next';
import { resolvePageComponent } from 'laravel-vite-plugin/inertia-helpers';
import { createPinia } from 'pinia';
import type { DefineComponent } from 'vue';
import { createApp, h } from 'vue';
import { createI18n } from 'vue-i18n';
import { ZiggyVue } from 'ziggy-js';
import { loadLocaleMessages } from './lang/loadTranslations';

interface PageProps {
    locale?: string;
    [key: string]: any;
}

const messages = loadLocaleMessages() as Readonly<Record<string, Record<string, any>>>;

console.log('messages', messages['el']);

const appName = import.meta.env.VITE_APP_NAME || 'Laravel';

createInertiaApp({
    title: (title) => (title ? `${title} | ${appName}` : appName),
    resolve: (name) => resolvePageComponent(`./pages/${name}.vue`, import.meta.glob<DefineComponent>('./pages/**/*.vue')),
    setup({ el, App, props, plugin }) {
        const pageProps = props.initialPage.props as PageProps;
        const i18n = createI18n({
            legacy: false,
            locale: pageProps.locale || 'el',
            fallbackLocale: 'el',
            messages,
            globalInjection: true,
        });

        createApp({ render: () => h(App, props) })
            .use(plugin)
            .use(createPinia())
            .use(ZiggyVue)
            .use(createBootstrap())
            .use(i18n)
            .mount(el);
    },
    progress: {
        color: '#4B5563',
    },
});
