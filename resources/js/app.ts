import '@/scss/app.scss';
import '@/scss/icons.scss';
import 'bootstrap-vue-next/dist/bootstrap-vue-next.css';
import 'flatpickr/dist/flatpickr.css';

import { createInertiaApp } from '@inertiajs/vue3';
import { createBootstrap } from 'bootstrap-vue-next';
import { resolvePageComponent } from 'laravel-vite-plugin/inertia-helpers';
import { createPinia } from 'pinia';
import type { DefineComponent } from 'vue';
import { createApp, h } from 'vue';
import { createI18n } from 'vue-i18n';
import { ZiggyVue } from 'ziggy-js';
import { loadLocaleMessages } from './lang/loadTranslations';
const messages = loadLocaleMessages() as Record<string, Record<string, any>>;

const appName = import.meta.env.VITE_APP_NAME;

createInertiaApp({
    title: (title) => (title ? `${title} | ${appName}` : appName),
    resolve: (name) => resolvePageComponent(`./pages/${name}.vue`, import.meta.glob<DefineComponent>('./pages/**/*.vue')),
    setup({ el, App, props, plugin }) {
        const i18n = createI18n({
            legacy: false,
            locale: props.initialPage.props.locale || 'gr',
            fallbackLocale: 'gr',
            messages,
            globalInjection: true,
        });

        createApp({ render: () => h(App, props) })
            .use(plugin)
            .use(createPinia())
            .use(ZiggyVue)
            .use(createBootstrap())
            .use(i18n)
            .mount(el);
    },
    progress: {
        color: '#4B5563',
    },
});

const langFiles = import.meta.glob<{ default: Record<string, string> }>('./lang-json/*/*.json');
async function resolveLang(lang: string) {
    const messages: { [p: string]: object } = {
        [lang]: {},
    };
    const paths = Object.keys(langFiles).filter((path) => path.includes(`/${lang}/`));

    console.log('paths', paths);

    for (const path of paths) {
        const module = await langFiles[path]();

        const key = path.split('/').pop()?.replace('.json', '');
        if (key) {
            Object.assign(messages[lang], { [key]: module.default });
        }
    }
    console.log('messages', messages);
    return messages;
}
