<template>
    <div class="tiptap-toolbar" v-if="editor">
        <button @click="editor.chain().focus().toggleBold().run()" :class="{ active: editor.isActive('bold') }">Bold</button>
        <button @click="editor.chain().focus().toggleItalic().run()" :class="{ active: editor.isActive('italic') }">Italic</button>
        <button @click="editor.chain().focus().toggleUnderline().run()" :class="{ active: editor.isActive('underline') }">Underline</button>
        <button @click="editor.chain().focus().toggleStrike().run()" :class="{ active: editor.isActive('strike') }">Strike</button>
        <button @click="editor.chain().focus().setParagraph().run()">Paragraph</button>
        <button @click="editor.chain().focus().toggleHeading({ level: 1 }).run()">H1</button>
        <button @click="editor.chain().focus().toggleHeading({ level: 2 }).run()">H2</button>
        <button @click="editor.chain().focus().toggleBulletList().run()">Bullet List</button>
        <button @click="editor.chain().focus().toggleOrderedList().run()">Ordered List</button>
        <button @click="editor.chain().focus().toggleBlockquote().run()">Blockquote</button>
        <button @click="editor.chain().focus().toggleCode().run()">Code</button>
        <button @click="editor.chain().focus().toggleCodeBlock().run()">Code Block</button>
        <button @click="editor.chain().focus().setHorizontalRule().run()">HR</button>
        <button
            @click="
                editor
                    .chain()
                    .focus()
                    .setLink({ href: prompt('URL') })
                    .run()
            "
        >
            Link
        </button>
        <button
            @click="
                editor
                    .chain()
                    .focus()
                    .setImage({ src: prompt('Image URL') })
                    .run()
            "
        >
            Image
        </button>
        <button @click="editor.chain().focus().setColor('#58a6ff').run()">Blue</button>
        <button @click="editor.chain().focus().setTextAlign('left').run()">Left</button>
        <button @click="editor.chain().focus().setTextAlign('center').run()">Center</button>
        <button @click="editor.chain().focus().setTextAlign('right').run()">Right</button>
        <button @click="editor.chain().focus().setTextAlign('justify').run()">Justify</button>
        <!-- Add more as needed -->
    </div>
</template>

<script setup lang="ts">
import type { Editor } from '@tiptap/vue-3';
defineProps<{
    editor: Editor;
}>();
</script>

<style scoped>
.tiptap-toolbar {
    display: flex;
    flex-wrap: wrap;
    gap: 4px;
    background: #f8f9fa;
    padding: 6px;
    border-radius: 4px 4px 0 0;
}
button {
    background: #fff;
    border: 1px solid #e3e3e3;
    padding: 6px 10px;
    border-radius: 4px;
    cursor: pointer;
    font-size: 14px;
    transition: background 0.2s;
}
button.active,
button:hover {
    background: #e3eafe;
    color: #2563eb;
}
</style>
