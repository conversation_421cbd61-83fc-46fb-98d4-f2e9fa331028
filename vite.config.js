import vue from '@vitejs/plugin-vue';
import { BootstrapVueNextResolver } from 'bootstrap-vue-next';
import laravel from 'laravel-vite-plugin';
import { resolve } from 'node:path';
import path from 'path';
import Components from 'unplugin-vue-components/vite';
import { defineConfig, loadEnv } from 'vite';

export default defineConfig(({ mode }) => {
    const env = loadEnv(mode, process.cwd(), '');
    const isDev = mode === 'development';

    return {
        server: {
            host: '0.0.0.0',
            port: 5174,
            strictPort: true,
            cors: true,
            watch: {
                usePolling: true,
                interval: 300,
            },
            hmr: {
                host: 'localhost',
            },
        },
        plugins: [
            laravel({
                input: ['resources/js/app.ts'],
                ssr: 'resources/js/ssr.ts',
                refresh: true,
            }),
            vue({
                template: {
                    transformAssetUrls: {
                        base: null,
                        includeAbsolute: false,
                    },
                },
            }),
            Components({
                resolvers: [BootstrapVueNextResolver()],
            }),
        ],
        resolve: {
            alias: {
                '@/': path.resolve(__dirname, './resources/js'),
                '@/images': path.resolve(__dirname, 'resources/images'),
                '@/scss': path.resolve(__dirname, 'resources/scss'),
                'ziggy-js': resolve(__dirname, 'vendor/tightenco/ziggy'),
            },
        },
    };
});
