<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Database\Eloquent\Relations\HasMany;

class Room extends Model
{
    /** @use HasFactory<\Database\Factories\RoomFactory> */
    use HasFactory;

    protected $fillable = ['title', 'description', 'adminId'];

    public function admin(): BelongsTo
    {
        return $this->belongsTo(User::class, 'adminId');
    }

    public function tags(): BelongsToMany
    {
        return $this->belongsToMany(Tag::class, 'room_tags', 'roomId', 'tagId')->withTimestamps();
    }

    public function users(): BelongsToMany
    {
        return $this->belongsToMany(User::class, 'room_users', 'roomId', 'userId')->withTimestamps();
    }
}
