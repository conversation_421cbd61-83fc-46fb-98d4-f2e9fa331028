<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\File;

class ExportLangAsJson extends Command
{
    protected $signature = 'lang:export-json';
    protected $description = 'Export Laravel PHP lang files as JSON to resources/lang-json';

    public function handle()
    {
        $langPath = base_path('lang');
        $outputPath = resource_path('js/lang');

        $locales = array_filter(
            scandir($langPath),
            fn($dir) => $dir !== '.' && $dir !== '..' && is_dir($langPath . DIRECTORY_SEPARATOR . $dir)
        );

        foreach ($locales as $locale) {
            $localePath = $langPath . DIRECTORY_SEPARATOR . $locale;
            $jsonLocalePath = $outputPath . DIRECTORY_SEPARATOR . $locale;

            if (!File::exists($jsonLocalePath)) {
                File::makeDirectory($jsonLocalePath, 0755, true);
            }

            $files = File::files($localePath);
            $jsonFiles = [];

            foreach ($files as $file) {
                if ($file->getExtension() !== 'php') {
                    continue;
                }

                $key = $file->getFilenameWithoutExtension();

                // Directly include the PHP file to get translations
                $translations = include $file->getPathname();

                // Ensure we have an array
                if (!is_array($translations)) {
                    $this->warn("Skipping {$file->getFilename()} - not a valid translation array");
                    continue;
                }

                File::put(
                    $jsonLocalePath . DIRECTORY_SEPARATOR . $key . '.json',
                    json_encode($translations, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE)
                );

                // Keep track of created JSON files for index.js
                $jsonFiles[] = $key;
            }

            // Create index.js file for this locale
            $this->createIndexFile($jsonLocalePath, $jsonFiles);
        }

        $this->info('Language files have been exported to lang-json!');
    }

    /**
     * Create an index.js file that imports and exports all JSON files
     */
    private function createIndexFile($localePath, $jsonFiles)
    {
        if (empty($jsonFiles)) {
            return;
        }

        $imports = [];
        $exports = [];

        foreach ($jsonFiles as $file) {
            $imports[] = "import {$file} from './{$file}.json'";
            $exports[] = "...{$file}";
        }

        $indexContent = implode("\n", $imports) . "\n\n";
        $indexContent .= "export default { " . implode(", ", $exports) . " }";

        File::put($localePath . DIRECTORY_SEPARATOR . 'index.js', $indexContent);
    }
}
