<?php

namespace App\Repositories;

use App\DTO\Requests\RoomRequestDTO;
use App\Models\Room;
use Illuminate\Pagination\LengthAwarePaginator;
use Illuminate\Support\Collection;

class RoomRepository
{
    public function getRooms(RoomRequestDTO $roomRequestDTO): LengthAwarePaginator|Collection
    {
        $query = Room::query();

        if ($roomRequestDTO->id) {
            $query->where('id', $roomRequestDTO->id);
        }

        if(count($roomRequestDTO->with)) {
            $query->with($roomRequestDTO->with);
        }

        if ($roomRequestDTO->searchKey) {
            $query->where('title', 'like', '%'.$roomRequestDTO->searchKey.'%')
                ->orWhere('description', 'like', '%'.$roomRequestDTO->searchKey.'%');
        }

        $query->{$roomRequestDTO->order === 'asc' ? 'orderBy' : 'orderByDesc'}($roomRequestDTO->orderBy);

        if ($roomRequestDTO->all) {
            return $query->get();
        }

        return $query->paginate($roomRequestDTO->rpp);
    }

    public function upsert(array $data): Room
    {
        if ($data['id'] ?? null) {
            $room = Room::find($data['id']);
        } else {
            $room = new Room();
        }

        $room->fill($data);
        $room->save();

        $room->tags()
            ->sync($data['tagIds']);
        $room->users()
            ->sync($data['userIds']);

        return $room;
    }

    public function deleteRoom(int $roomId): void
    {
        Room::find($roomId)->delete();
    }
}
