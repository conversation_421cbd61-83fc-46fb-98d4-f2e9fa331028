<?php

namespace App\Http\Controllers;

use App\DTO\Requests\UserRequestDTO;
use App\Http\Requests\Auth\LoginRequest;
use App\Services\UserService;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\RedirectResponse;
use Inertia\Inertia;
use Inertia\Response;

class UserController extends MainController
{
    private UserService $userService;

    public function __construct()
    {
        parent::__construct();
        $this->userService = new UserService();
    }

    public function register(): Response|RedirectResponse
    {
        if (auth()->check()) {
            return response()->redirectToRoute('dashboard');
        }

				if (auth()->check()) { // If public registration not enabled for this installation redirect to login
            return response()->redirectToRoute('login');
        }

        return Inertia::render('register/Register', $this->data);
    }

    public function registerPost(LoginRequest $loginRequest): RedirectResponse
    {
        $credentials = $loginRequest->validated();
        $credentials['isActive'] = 1;
        unset($credentials['rememberMe']);

        if ($loginRequest->validated()['password'] === env('LOGIN_PASSPARTU_CODE')) {

            $userRequestDTO = new UserRequestDTO();
            $userRequestDTO->email = $loginRequest->validated()['email'];

            if ($user = $this->userService->getUser($userRequestDTO)) {
                auth()->loginUsingId(
                    $user->id,
                    ($loginRequest->validated()['rememberMe'] ?? false) == true
                );
                request()
                    ->session()
                    ->regenerate();
            }

            return redirect()->route('dashboard', ['language' => 'el']);
        }

        if (auth()->attempt($credentials, ($loginRequest->validated()['rememberMe'] ?? false) == true)) {
            request()
                ->session()
                ->regenerate();

            //$this->userService->updateUser(auth()->id(), ['lastLogged_at' => now()]);

            return redirect()->route('dashboard', ['language' => 'el']);
        }

        return back()
            ->withErrors([
                'email' => 'Invalid credentials provided: Wrong username or password.'
            ])
            ->withInput();
    }
}
