<?php

namespace App\Http\Controllers;

use App\DTO\Requests\TagRequestDTO;
use App\Http\Requests\Tag\AddTagRequest;
use App\Http\Requests\Tag\MassDeleteTagRequest;
use App\Http\Requests\Tag\UpdateTagRequest;
use App\Services\TagService;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\RedirectResponse;
use Illuminate\Support\Facades\Session;
use Inertia\Inertia;
use Inertia\Response;

class TagController extends MainController
{
    private TagService $tagService;

    public function __construct()
    {
        parent::__construct();
        $this->tagService = new TagService();
    }

    public function index(): Response
    {
        $this->data['searchKey'] = Session::get('tag_searchKey') ?? '';

        $tagRequestDTO = new TagRequestDTO();
        $tagRequestDTO->searchKey = $this->data['searchKey'];
        $this->data['tags'] = $this->tagService->getTags($tagRequestDTO);

        return Inertia::render('tag/index', $this->data);
    }

    public function indexPost(): RedirectResponse
    {
        Session::put('tag_searchKey', (request()->post()['searchKey'] ?? ''));

        return response()->redirectToRoute('tagIndex', setLocaleParams());
    }

    public function deleteTag(int $id): RedirectResponse
    {
        $this->tagService->deleteTag($id);

        return redirect()->back();
    }

    public function addTag(): Response
    {
        return Inertia::render('tag/details', $this->data);
    }

    public function addTagPost(AddTagRequest $addTagRequest): RedirectResponse
    {
        $data = $addTagRequest->validated();
        $tag = $this->tagService->upsert($data);

        return redirect()->route('tagAdd');
    }

    public function updateTag(): Response
    {
        $tagRequestDTO = new TagRequestDTO();
        $tagRequestDTO->id = request()->route('id');
        $this->data['tag'] = $this->tagService->getTag($tagRequestDTO);

        return Inertia::render('tag/details', $this->data);
    }

    public function updateTagPost(UpdateTagRequest $updateTagRequest): RedirectResponse
    {
        $this->tagService->upsert($updateTagRequest->validated());

        return redirect()
            ->back()
            ->with('success', 'Tag updated successfully.');
    }

    public function massDeleteTags(MassDeleteTagRequest $massDeleteTagRequest): JsonResponse
    {
        $tagIds = $massDeleteTagRequest->validated()['ids'];
        $this->tagService->massDelete($tagIds);

        return response()->json();
    }
}
