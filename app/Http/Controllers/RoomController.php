<?php

namespace App\Http\Controllers;

use App\DTO\Requests\RoomRequestDTO;
use App\DTO\Requests\TagRequestDTO;
use App\DTO\Requests\UserRequestDTO;
use App\Http\Requests\Room\AddRoomRequest;
use App\Http\Requests\Room\UpdateRoomRequest;
use App\Services\RoomService;
use App\Services\TagService;
use App\Services\UserService;
use Illuminate\Http\RedirectResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Session;
use Inertia\Inertia;
use Inertia\Response;

class RoomController extends MainController
{
    private RoomService $roomService;
    private TagService $tagService;
    private UserService $userService;

    public function __construct()
    {
        parent::__construct();
        $this->roomService = new RoomService();
        $this->tagService = new TagService();
        $this->userService = new UserService();
    }

    public function index(): Response
    {
        $this->data['searchKey'] = Session::get('room_searchKey') ?? '';

        $roomRequestDTO = new RoomRequestDTO();
        $roomRequestDTO->searchKey = $this->data['searchKey'];
        $this->data['rooms'] = $this->roomService->getRooms($roomRequestDTO);

        return Inertia::render('room/index', $this->data);
    }

    public function indexPost(): RedirectResponse
    {
        Session::put('room_searchKey', (request()->post()['searchKey'] ?? ''));
        return response()->redirectToRoute('roomIndex', setLocaleParams());
    }

    public function deleteRoom(int $id): RedirectResponse
    {
        $this->roomService->deleteRoom($id);
        return redirect()->back();
    }

    public function addRoom(): Response
    {
        $tagRequestDTO = new TagRequestDTO();
        $tagRequestDTO->all = true;
        $this->data['tags'] = $this->tagService->getTags($tagRequestDTO);

        $userRequestDTO = new UserRequestDTO();
        $userRequestDTO->all = true;
        $this->data['users'] = $this->userService->getUsers($userRequestDTO);

        return Inertia::render('room/details', $this->data);
    }

    public function addRoomPost(AddRoomRequest $addRoomRequest): RedirectResponse
    {
        $data = $addRoomRequest->validated();
        $room = $this->roomService->upsert($data);

        return response()->redirectToRoute('updateRoom', setLocaleParams(['id' => $room->id]));
    }

    public function updateRoom(): Response
    {
        $roomRequestDTO = new RoomRequestDTO();
        $roomRequestDTO->id = request()->route('id');
        $roomRequestDTO->with = ['tags', 'users'];
        $this->data['room'] = $this->roomService->getRoom($roomRequestDTO);

        $tagRequestDTO = new TagRequestDTO();
        $tagRequestDTO->all = true;
        $this->data['tags'] = $this->tagService->getTags($tagRequestDTO);

        $userRequestDTO = new UserRequestDTO();
        $userRequestDTO->all = true;
        $this->data['users'] = $this->userService->getUsers($userRequestDTO);

        return Inertia::render('room/details', $this->data);
    }

    public function updateRoomPost(UpdateRoomRequest $updateRoomRequest): RedirectResponse
    {
        $this->roomService->upsert($updateRoomRequest->validated());

        return redirect()->back()->with('success', 'Room updated successfully.');
    }

}
